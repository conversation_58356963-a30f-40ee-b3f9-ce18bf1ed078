@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base styles for the design system */
@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    @apply text-neutral-900 bg-white antialiased;
    font-feature-settings: 'kern' 1, 'liga' 1;
  }

  /* Typography base styles matching reference design */
  h1, h2, h3, h4, h5, h6 {
    @apply font-heading text-neutral-900;
    letter-spacing: -0.025em;
  }

  h1 {
    @apply text-hero-mobile md:text-hero font-bold;
    line-height: 1.15;
  }

  h2 {
    @apply text-section-mobile md:text-section font-semibold;
    line-height: 1.3;
  }

  h3 {
    @apply text-2xl md:text-3xl font-semibold;
    line-height: 1.35;
  }

  h4 {
    @apply text-xl md:text-2xl font-medium;
    line-height: 1.4;
  }

  p {
    @apply text-base md:text-lg text-neutral-600;
    line-height: 1.6;
  }

  /* Focus styles for accessibility */
  *:focus {
    @apply outline-none ring-2 ring-primary-500 ring-offset-2;
  }

  /* Button focus styles */
  button:focus,
  a:focus {
    @apply outline-none ring-2 ring-primary-500 ring-offset-2;
  }
}

/* Component styles */
@layer components {
  /* Container styles matching reference design */
  .container-custom {
    @apply max-w-6xl mx-auto px-6 sm:px-8 lg:px-10;
  }

  /* Narrow container for content-focused sections */
  .container-narrow {
    @apply max-w-4xl mx-auto px-6 sm:px-8 lg:px-10;
  }

  /* Wide container for full-width sections */
  .container-wide {
    @apply max-w-7xl mx-auto px-6 sm:px-8 lg:px-12;
  }

  /* Extra wide container for hero sections */
  .container-hero {
    @apply max-w-screen-xl mx-auto px-6 sm:px-8 lg:px-10;
  }

  /* Section padding matching reference design */
  .section-padding {
    @apply py-16 md:py-24 lg:py-32;
  }

  /* Compact section padding for dense content */
  .section-padding-compact {
    @apply py-12 md:py-16 lg:py-20;
  }

  /* Large section padding for hero sections */
  .section-padding-large {
    @apply py-20 md:py-32 lg:py-40;
  }

  /* Extra large padding for major sections */
  .section-padding-xl {
    @apply py-24 md:py-36 lg:py-48;
  }

  /* Card styles with enhanced spacing */
  .card {
    @apply bg-white rounded-card shadow-card hover:shadow-card-hover transition-all duration-300;
  }

  /* Card with generous internal padding */
  .card-padded {
    @apply card p-8 md:p-10 lg:p-12;
  }

  /* Card with compact padding */
  .card-compact {
    @apply card p-4 md:p-6;
  }

  /* Content spacing utilities */
  .content-spacing {
    @apply space-y-6 md:space-y-8 lg:space-y-10;
  }

  .content-spacing-tight {
    @apply space-y-4 md:space-y-6;
  }

  .content-spacing-loose {
    @apply space-y-8 md:space-y-12 lg:space-y-16;
  }

  /* Button styles matching reference design */
  .btn-primary {
    @apply bg-primary-500 hover:bg-primary-600 text-white font-semibold px-8 py-3.5 rounded-lg transition-all duration-200 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 shadow-sm hover:shadow-md;
    letter-spacing: -0.01em;
  }

  .btn-secondary {
    @apply bg-white hover:bg-neutral-50 text-primary-500 font-semibold px-8 py-3.5 rounded-lg border-2 border-primary-500 transition-all duration-200 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 shadow-sm hover:shadow-md;
    letter-spacing: -0.01em;
  }

  .btn-ghost {
    @apply bg-transparent hover:bg-white/10 text-white font-semibold px-8 py-3.5 rounded-lg border-2 border-white/30 hover:border-white/50 transition-all duration-200 focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-transparent;
    letter-spacing: -0.01em;
  }

  .btn-outline {
    @apply bg-transparent hover:bg-primary-50 text-primary-600 font-semibold px-8 py-3.5 rounded-lg border-2 border-primary-200 hover:border-primary-300 transition-all duration-200 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
    letter-spacing: -0.01em;
  }

  /* Text styles */
  .text-gradient {
    @apply bg-gradient-to-r from-primary-500 to-brand-500 bg-clip-text text-transparent;
  }

  /* Hero overlay */
  .hero-overlay {
    @apply absolute inset-0 bg-gradient-to-r from-dark-900/70 via-dark-900/50 to-dark-900/30;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounceGentle {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}
