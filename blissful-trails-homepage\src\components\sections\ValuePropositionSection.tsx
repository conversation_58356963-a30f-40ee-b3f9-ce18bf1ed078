'use client';

import { motion } from 'framer-motion';
import { VALUE_PROPOSITIONS } from '@/data/content';
import EnhancedIcon, { IconName } from '@/components/ui/EnhancedIcon';

export default function ValuePropositionSection() {
  return (
    <section className="section-padding bg-neutral-50">
      <div className="container-custom">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <h2 className="text-section-mobile md:text-section font-heading font-semibold text-neutral-900 mb-6">
            Your Gateway to Authentic Thai Adventures
          </h2>
          <p className="text-lg md:text-xl text-neutral-600 max-w-4xl mx-auto leading-relaxed">
            At Blissful Trails, we understand what makes a perfect Thailand experience. From ancient temples to bustling markets, we curate adventures that capture the true spirit of Thailand.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {VALUE_PROPOSITIONS.map((item, index) => {
            return (
              <motion.div
                key={item.id}
                className="bg-white rounded-xl p-8 text-center group hover:shadow-lg transition-all duration-300 border border-neutral-100"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                data-testid="value-prop-card"
              >
                <div className="w-16 h-16 bg-primary-50 rounded-2xl flex items-center justify-center mb-6 mx-auto group-hover:bg-primary-100 transition-all duration-300">
                  <EnhancedIcon
                    name={item.icon as IconName}
                    size="xl"
                    variant="primary"
                    className="text-primary-600 group-hover:text-primary-700 transition-colors duration-300"
                  />
                </div>
                <h3 className="text-lg md:text-xl font-heading font-semibold text-neutral-900 mb-4">
                  {item.title}
                </h3>
                <p className="text-neutral-600 leading-relaxed text-sm md:text-base">
                  {item.description}
                </p>
              </motion.div>
            );
          })}
        </div>
      </div>
    </section>
  );
}
