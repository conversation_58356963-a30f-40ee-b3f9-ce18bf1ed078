export const HERO_CONTENT = {
  headline: "Discover the Magic of Thailand: Your Ultimate Adventure Awaits",
  subheadline: "From ancient temples to pristine beaches",
  description: "Experience Thailand like never before with our expertly curated adventures and authentic local experiences. Discover hidden gems, immerse in rich culture, and create unforgettable memories.",
  primaryCTA: "Explore",
  secondaryCTA: "Learn More",
  trustBadges: [
    "Expert Guides",
    "Authentic Experiences",
    "24/7 Support"
  ]
} as const;

export const VALUE_PROPOSITIONS = [
  {
    id: 'verified-cabs',
    icon: 'Car',
    title: 'Verified Clean Cabs',
    description: 'Sanitized vehicles with first aid kits and experienced drivers familiar with hill routes.',
  },
  {
    id: 'hygiene-rooms',
    icon: 'Shield',
    title: 'Hygiene Certified Stays',
    description: 'Hand-picked accommodations with verified cleanliness standards and family-friendly amenities.',
  },
  {
    id: 'medical-support',
    icon: 'Heart',
    title: 'Emergency Medical Kit',
    description: 'Every trip includes basic medical supplies and access to local healthcare networks.',
  },
  {
    id: 'custom-itinerary',
    icon: 'Map',
    title: 'Personalized Planning',
    description: 'Customizable itineraries based on your family preferences, budget, and travel dates.',
  },
  {
    id: 'local-guides',
    icon: 'Users',
    title: 'Multilingual Guides',
    description: 'Local experts who speak Hindi, Bengali, and English to ensure smooth communication.',
  },
  {
    id: 'flexible-booking',
    icon: 'Calendar',
    title: 'Flexible Cancellation',
    description: 'Easy modifications and cancellations up to 48 hours before travel with full refund.',
  },
] as const;

export const PACKAGES_DATA = [
  {
    id: 'darjeeling-classic',
    category: 'Hill Stations',
    title: 'Darjeeling Classic Family Package',
    description: 'Experience the Queen of Hills with toy train rides, tea gardens, and stunning sunrise views from Tiger Hill.',
    image: '/images/packages/darjeeling-package.webp',
    duration: '4 Days / 3 Nights',
    priceRange: '₹15,000 - ₹25,000',
    rating: 4.8,
    reviewCount: 156,
    highlights: [
      'Toy Train Experience',
      'Tiger Hill Sunrise',
      'Tea Garden Tours',
      'Mall Road Shopping'
    ],
    tags: ['Family Friendly', 'Hill Station', 'Tea Gardens'],
    isPopular: true,
    region: 'Darjeeling'
  },
  {
    id: 'sikkim-adventure',
    category: 'Adventure Tours',
    title: 'Sikkim Adventure & Culture',
    description: 'Explore monasteries, pristine lakes, and mountain vistas in the Land of Thunderbolt.',
    image: '/images/packages/sikkim-package.webp',
    duration: '5 Days / 4 Nights',
    priceRange: '₹20,000 - ₹35,000',
    rating: 4.9,
    reviewCount: 89,
    highlights: [
      'Tsomgo Lake Visit',
      'Rumtek Monastery',
      'Gangtok City Tour',
      'Cable Car Rides'
    ],
    tags: ['Adventure', 'Culture', 'Mountains'],
    isPopular: false,
    region: 'Sikkim'
  },
  {
    id: 'dooars-wildlife',
    category: 'Dooars Wildlife',
    title: 'Dooars Wildlife Safari',
    description: 'Discover exotic wildlife in Jaldapara and Gorumara National Parks with comfortable jungle stays.',
    image: '/images/packages/dooars-package.webp',
    duration: '3 Days / 2 Nights',
    priceRange: '₹12,000 - ₹18,000',
    rating: 4.7,
    reviewCount: 124,
    highlights: [
      'Elephant Safari',
      'Bird Watching',
      'Jungle Lodge Stay',
      'Nature Walks'
    ],
    tags: ['Wildlife', 'Safari', 'Nature'],
    isPopular: true,
    region: 'Dooars'
  },
  {
    id: 'kalimpong-heritage',
    category: 'Cultural Experiences',
    title: 'Kalimpong Heritage Trail',
    description: 'Immerse in local culture with monastery visits, handicraft workshops, and scenic valley views.',
    image: '/images/packages/kalimpong-package.webp',
    duration: '3 Days / 2 Nights',
    priceRange: '₹10,000 - ₹16,000',
    rating: 4.6,
    reviewCount: 67,
    highlights: [
      'Zang Dhok Palri Monastery',
      'Flower Nurseries',
      'Handicraft Centers',
      'Valley Views'
    ],
    tags: ['Heritage', 'Culture', 'Peaceful'],
    isPopular: false,
    region: 'Kalimpong'
  },
  {
    id: 'complete-north-bengal',
    category: 'Family Getaways',
    title: 'Complete North Bengal Experience',
    description: 'Comprehensive tour covering Darjeeling, Sikkim, and Dooars for the ultimate family adventure.',
    image: '/images/packages/north-bengal-package.webp',
    duration: '8 Days / 7 Nights',
    priceRange: '₹35,000 - ₹55,000',
    rating: 4.9,
    reviewCount: 203,
    highlights: [
      'Multi-destination Tour',
      'All Major Attractions',
      'Comfortable Transportation',
      'Expert Local Guides'
    ],
    tags: ['Comprehensive', 'Family', 'Multi-destination'],
    isPopular: true,
    region: 'Multi-region'
  },
  {
    id: 'mirik-peaceful',
    category: 'Family Getaways',
    title: 'Mirik Peaceful Retreat',
    description: 'Serene lakeside retreat perfect for families seeking tranquility away from crowded destinations.',
    image: '/images/packages/mirik-package.webp',
    duration: '2 Days / 1 Night',
    priceRange: '₹8,000 - ₹12,000',
    rating: 4.5,
    reviewCount: 45,
    highlights: [
      'Sumendu Lake Boating',
      'Orange Gardens',
      'Peaceful Environment',
      'Family Activities'
    ],
    tags: ['Peaceful', 'Lake', 'Short Trip'],
    isPopular: false,
    region: 'Mirik'
  }
] as const;

export const PACKAGE_CATEGORIES = [
  'All Packages',
  'Hill Stations',
  'Dooars Wildlife',
  'Family Getaways',
  'Adventure Tours',
  'Cultural Experiences'
] as const;

export const PACKAGE_FILTERS = {
  regions: ['All Regions', 'Darjeeling', 'Sikkim', 'Dooars', 'Kalimpong', 'Mirik', 'Multi-region'],
  duration: ['Any Duration', '1-2 Days', '3-4 Days', '5-6 Days', '7+ Days'],
  budget: ['Any Budget', 'Under ₹15,000', '₹15,000 - ₹25,000', '₹25,000 - ₹40,000', 'Above ₹40,000']
} as const;

export const HOW_IT_WORKS_STEPS = [
  {
    id: 'select-package',
    step: 1,
    title: 'Select Package',
    description: 'Browse our curated travel packages or tell us your preferences for a custom itinerary.',
    icon: 'Search',
    details: [
      'Browse 50+ pre-designed packages',
      'Filter by destination, budget & duration',
      'Read reviews from verified travelers',
      'Compare package features & inclusions'
    ],
    interactiveElement: 'Package Selector',
    estimatedTime: '5-10 minutes'
  },
  {
    id: 'customize-trip',
    step: 2,
    title: 'Add Food & Stops',
    description: 'Customize your journey with preferred meals, additional stops, and special requirements.',
    icon: 'Settings',
    details: [
      'Choose meal preferences (Veg/Non-veg/Jain)',
      'Add scenic stops & photo points',
      'Select accommodation upgrades',
      'Include special activities or experiences'
    ],
    interactiveElement: 'Customization Options',
    estimatedTime: '10-15 minutes'
  },
  {
    id: 'confirm-booking',
    step: 3,
    title: 'Confirm Booking',
    description: 'Review your itinerary, make secure payment, and receive instant confirmation.',
    icon: 'CreditCard',
    details: [
      'Review complete itinerary & pricing',
      'Secure payment via UPI/Cards/Net Banking',
      'Instant booking confirmation via SMS/Email',
      'Download detailed travel documents'
    ],
    interactiveElement: 'Secure Payment Gateway',
    estimatedTime: '5 minutes'
  },
  {
    id: 'travel-worry-free',
    step: 4,
    title: 'Travel Worry-Free',
    description: 'Enjoy your journey with 24/7 support, real-time updates, and emergency assistance.',
    icon: 'Shield',
    details: [
      '24/7 WhatsApp support during travel',
      'Real-time driver & vehicle tracking',
      'Emergency medical assistance',
      'Instant rebooking for weather disruptions'
    ],
    interactiveElement: 'Real-time Support',
    estimatedTime: 'Throughout your journey'
  }
] as const;

export const HOW_IT_WORKS_CONTENT = {
  headline: 'How It Works',
  subheadline: 'Your Journey to Blissful Travel in 4 Simple Steps',
  description: 'From selection to return, we have streamlined every aspect of your travel experience. Our proven process ensures hassle-free planning and unforgettable memories.',
  ctaText: 'Start Planning Now',
  trustMessage: 'Trusted by 10,000+ families across India'
} as const;

export const TRUST_SAFETY_CONTENT = {
  headline: 'Your Safety is Our Priority',
  subheadline: 'Travel with Complete Peace of Mind',
  description: 'We understand that family safety comes first. Our comprehensive safety measures and verified partners ensure your journey is secure, hygienic, and worry-free.',
  features: [
    {
      id: 'hygiene-verified',
      icon: 'ShieldCheck',
      title: 'Hygiene Verified Accommodations',
      description: 'All hotels and homestays undergo rigorous cleanliness inspections with certified hygiene ratings.',
      details: [
        'Regular sanitization protocols',
        'Verified cleanliness certificates',
        'Family-friendly amenities',
        'Quality assurance checks'
      ],
      badge: 'Certified Clean'
    },
    {
      id: 'emergency-support',
      icon: 'Phone',
      title: '24/7 Emergency Support',
      description: 'Round-the-clock assistance with dedicated helpline for medical emergencies and travel disruptions.',
      details: [
        'Instant WhatsApp support',
        'Medical emergency assistance',
        'Weather disruption rebooking',
        'Local emergency contacts'
      ],
      badge: 'Always Available'
    },
    {
      id: 'verified-drivers',
      icon: 'UserCheck',
      title: 'Background Verified Drivers',
      description: 'All drivers undergo police verification, medical checkups, and route familiarity training.',
      details: [
        'Police background verification',
        'Medical fitness certificates',
        'Hill route expertise',
        'Customer service training'
      ],
      badge: 'Trusted Professionals'
    },
    {
      id: 'medical-kit',
      icon: 'Heart',
      title: 'Emergency Medical Kit',
      description: 'Every vehicle carries first aid supplies and emergency medical equipment for immediate assistance.',
      details: [
        'Basic first aid supplies',
        'Emergency medications',
        'Oxygen cylinder (hill routes)',
        'Emergency contact cards'
      ],
      badge: 'Medical Ready'
    },
    {
      id: 'insurance-coverage',
      icon: 'Shield',
      title: 'Travel Insurance Included',
      description: 'Comprehensive travel insurance covering medical emergencies, trip cancellations, and baggage protection.',
      details: [
        'Medical emergency coverage',
        'Trip cancellation protection',
        'Baggage loss coverage',
        'Emergency evacuation'
      ],
      badge: 'Fully Insured'
    },
    {
      id: 'real-time-tracking',
      icon: 'MapPin',
      title: 'Real-time Vehicle Tracking',
      description: 'GPS-enabled vehicles with live location sharing for family members and emergency contacts.',
      details: [
        'Live GPS tracking',
        'Family location sharing',
        'Route monitoring',
        'Emergency location alerts'
      ],
      badge: 'Always Connected'
    }
  ],
  certifications: [
    {
      name: 'ISO 9001:2015',
      description: 'Quality Management System',
      icon: 'Award'
    },
    {
      name: 'IATA Certified',
      description: 'International Air Transport Association',
      icon: 'Plane'
    },
    {
      name: 'Ministry of Tourism',
      description: 'Government of India Recognition',
      icon: 'Building'
    }
  ],
  stats: [
    {
      number: '10,000+',
      label: 'Families Served Safely',
      icon: 'Users'
    },
    {
      number: '99.8%',
      label: 'Safety Record',
      icon: 'Shield'
    },
    {
      number: '24/7',
      label: 'Emergency Support',
      icon: 'Clock'
    },
    {
      number: '100%',
      label: 'Verified Partners',
      icon: 'CheckCircle'
    }
  ],
  ctaText: 'Learn More About Our Safety Measures',
  trustMessage: 'Your family\'s safety is our commitment, not just a promise.'
} as const;

export const TESTIMONIALS_CONTENT = {
  headline: 'Stories from Our Happy Travelers',
  subheadline: 'Real Experiences, Real Memories',
  description: 'Discover why thousands of families choose Blissful Trails for their perfect getaway. Read authentic stories from travelers who experienced the magic of North Bengal and Sikkim.',
  ctaText: 'Share Your Story',
  trustMessage: 'Join 10,000+ families who trusted us with their precious memories'
} as const;

export const TESTIMONIALS_DATA = [
  {
    id: 'testimonial-1',
    name: 'Priya & Rajesh Sharma',
    location: 'Mumbai, Maharashtra',
    tripDestination: 'Darjeeling & Sikkim',
    category: 'Families with Kids',
    rating: 5,
    date: 'October 2024',
    image: '/images/testimonials/sharma-family.webp',
    quote: 'Our 8-day North Bengal trip with Blissful Trails was absolutely magical! The hygiene standards were exceptional, and our kids loved every moment. The toy train ride in Darjeeling was a highlight, and the driver was so patient with our family. Highly recommended for families!',
    highlights: [
      'Exceptional hygiene standards',
      'Kid-friendly activities',
      'Patient and caring driver',
      'Memorable toy train experience'
    ],
    packageBooked: 'Complete North Bengal Experience',
    verified: true,
    featured: true
  },
  {
    id: 'testimonial-2',
    name: 'Amit & Sneha Patel',
    location: 'Ahmedabad, Gujarat',
    tripDestination: 'Dooars Wildlife',
    category: 'Couples',
    rating: 5,
    date: 'September 2024',
    image: '/images/testimonials/patel-couple.webp',
    quote: 'Perfect romantic getaway! The wildlife safari at Jaldapara was breathtaking, and the jungle lodge was incredibly clean and comfortable. The 24/7 support team was always available when we needed them. Thank you for making our anniversary special!',
    highlights: [
      'Romantic wildlife experience',
      'Clean and comfortable accommodation',
      'Excellent 24/7 support',
      'Perfect anniversary celebration'
    ],
    packageBooked: 'Dooars Wildlife Safari',
    verified: true,
    featured: true
  },
  {
    id: 'testimonial-3',
    name: 'Ravi, Kiran & Friends',
    location: 'Bangalore, Karnataka',
    tripDestination: 'Sikkim Adventure',
    category: 'Friends',
    rating: 5,
    date: 'August 2024',
    image: '/images/testimonials/friends-group.webp',
    quote: 'Epic adventure with the squad! The Tsomgo Lake visit was surreal, and the monastery tours were so peaceful. Our guide spoke perfect English and knew all the best photo spots. The emergency medical kit came in handy when one of us had altitude sickness. Professional service!',
    highlights: [
      'Stunning Tsomgo Lake experience',
      'Knowledgeable English-speaking guide',
      'Emergency medical support',
      'Perfect group adventure'
    ],
    packageBooked: 'Sikkim Adventure & Culture',
    verified: true,
    featured: false
  },
  {
    id: 'testimonial-4',
    name: 'Meera & Suresh Gupta',
    location: 'Delhi, NCR',
    tripDestination: 'Kalimpong Heritage',
    category: 'Couples',
    rating: 4,
    date: 'July 2024',
    image: '/images/testimonials/gupta-couple.webp',
    quote: 'Peaceful and culturally rich experience. The monastery visits were enlightening, and the handicraft workshops were fascinating. The flower nurseries were a photographer\'s paradise. Great value for money and excellent service throughout.',
    highlights: [
      'Culturally enriching experience',
      'Beautiful flower nurseries',
      'Fascinating handicraft workshops',
      'Great value for money'
    ],
    packageBooked: 'Kalimpong Heritage Trail',
    verified: true,
    featured: false
  },
  {
    id: 'testimonial-5',
    name: 'Anita & Family',
    location: 'Kolkata, West Bengal',
    tripDestination: 'Mirik Peaceful Retreat',
    category: 'Families with Kids',
    rating: 5,
    date: 'June 2024',
    image: '/images/testimonials/anita-family.webp',
    quote: 'Perfect short getaway from Kolkata! The kids enjoyed boating at Sumendu Lake, and we loved the peaceful environment. The orange gardens were beautiful, and the local food was delicious. Clean accommodation and friendly staff made it memorable.',
    highlights: [
      'Perfect family short trip',
      'Enjoyable lake boating',
      'Beautiful orange gardens',
      'Clean and friendly service'
    ],
    packageBooked: 'Mirik Peaceful Retreat',
    verified: true,
    featured: false
  },
  {
    id: 'testimonial-6',
    name: 'Vikram & Pooja Singh',
    location: 'Pune, Maharashtra',
    tripDestination: 'Darjeeling Classic',
    category: 'Couples',
    rating: 5,
    date: 'May 2024',
    image: '/images/testimonials/singh-couple.webp',
    quote: 'Honeymoon perfection! The sunrise at Tiger Hill was breathtaking, and the tea garden tours were so romantic. Our driver was punctual and respectful. The hygiene standards exceeded our expectations. Blissful Trails made our honeymoon truly blissful!',
    highlights: [
      'Romantic honeymoon experience',
      'Breathtaking Tiger Hill sunrise',
      'Romantic tea garden tours',
      'Exceptional hygiene standards'
    ],
    packageBooked: 'Darjeeling Classic Family Package',
    verified: true,
    featured: true
  }
] as const;

export const TESTIMONIAL_CATEGORIES = [
  'All Stories',
  'Families with Kids',
  'Couples',
  'Friends'
] as const;

export const TESTIMONIAL_STATS = [
  {
    number: '4.9/5',
    label: 'Average Rating',
    icon: 'Star'
  },
  {
    number: '10,000+',
    label: 'Happy Travelers',
    icon: 'Users'
  },
  {
    number: '98%',
    label: 'Would Recommend',
    icon: 'ThumbsUp'
  },
  {
    number: '500+',
    label: 'Five Star Reviews',
    icon: 'Award'
  }
] as const;

export const APP_WHATSAPP_CTA_CONTENT = {
  headline: 'Stay Connected, Travel Better',
  subheadline: 'Get instant support and exclusive offers on your mobile',
  description: 'Download our app for seamless booking and chat with us on WhatsApp for instant assistance.',
  whatsappCTA: {
    text: 'Chat on WhatsApp',
    message: 'Hi! I\'m interested in planning a family trip with Blissful Trails. Can you help me with package details?',
    icon: 'MessageCircle'
  },
  appDownload: {
    headline: 'Download Our App',
    description: 'Book faster, get exclusive deals, and manage your trips on the go',
    playStore: {
      text: 'Get it on Google Play',
      url: 'https://play.google.com/store/apps/details?id=com.blissfultrails.app',
      icon: 'Smartphone'
    },
    appStore: {
      text: 'Download on App Store',
      url: 'https://apps.apple.com/app/blissful-trails/id123456789',
      icon: 'Smartphone'
    }
  },
  offers: [
    {
      id: 'app-exclusive',
      text: '10% OFF on first booking via app',
      code: 'APP10',
      badge: 'App Exclusive'
    },
    {
      id: 'whatsapp-instant',
      text: 'Instant booking confirmation on WhatsApp',
      badge: 'WhatsApp Only'
    }
  ],
  trustBadges: [
    '50,000+ Downloads',
    'Instant Support',
    'Secure Payments'
  ]
} as const;

export const FOOTER_CONTENT = {
  companyInfo: {
    name: 'Blissful Trails',
    tagline: 'Curated trips. Clean cabs. Cozy stays.',
    description: 'Your trusted partner for hassle-free family travel across North Bengal and Sikkim. Experience the perfect blend of adventure, comfort, and safety.',
    logo: '/images/logo.svg'
  },
  quickLinks: [
    { label: 'About Us', href: '/about' },
    { label: 'Our Packages', href: '#packages' },
    { label: 'How It Works', href: '#how-it-works' },
    { label: 'Safety Measures', href: '#trust-safety' },
    { label: 'Testimonials', href: '#testimonials' },
    { label: 'Contact Us', href: '#contact' }
  ],
  destinations: [
    { label: 'Darjeeling Tours', href: '/destinations/darjeeling' },
    { label: 'Sikkim Packages', href: '/destinations/sikkim' },
    { label: 'Dooars Wildlife', href: '/destinations/dooars' },
    { label: 'Kalimpong Trips', href: '/destinations/kalimpong' },
    { label: 'Gangtok Tours', href: '/destinations/gangtok' },
    { label: 'Custom Itinerary', href: '/custom-package' }
  ],
  support: [
    { label: '24/7 Help Center', href: '/support' },
    { label: 'WhatsApp Support', href: `https://wa.me/${'+919876543210'.replace(/[^0-9]/g, '')}` },
    { label: 'Emergency Assistance', href: '/emergency' },
    { label: 'Travel Insurance', href: '/insurance' },
    { label: 'Cancellation Policy', href: '/cancellation' },
    { label: 'FAQ', href: '/faq' }
  ],
  legal: [
    { label: 'Privacy Policy', href: '/privacy' },
    { label: 'Terms of Service', href: '/terms' },
    { label: 'Refund Policy', href: '/refund' },
    { label: 'Cookie Policy', href: '/cookies' }
  ],
  socialMedia: [
    { platform: 'Facebook', url: 'https://facebook.com/blissfultrails', icon: 'Facebook' },
    { platform: 'Instagram', url: 'https://instagram.com/blissfultrails', icon: 'Instagram' },
    { platform: 'Twitter', url: 'https://twitter.com/blissfultrails', icon: 'Twitter' },
    { platform: 'YouTube', url: 'https://youtube.com/blissfultrails', icon: 'Youtube' }
  ],
  newsletter: {
    headline: 'Stay Updated',
    description: 'Get exclusive deals and travel tips delivered to your inbox',
    placeholder: 'Enter your email address',
    buttonText: 'Subscribe',
    privacyText: 'We respect your privacy. Unsubscribe anytime.'
  },
  copyright: `© 2024 Blissful Trails. All rights reserved.`,
  certifications: [
    { name: 'ISO Certified', icon: 'Award' },
    { name: 'Travel Safe', icon: 'Shield' },
    { name: 'Verified Partner', icon: 'CheckCircle' }
  ]
} as const;

export const BOOKING_FORM_CONTENT = {
  headline: 'Plan Your Perfect Trip',
  subheadline: 'Tell us your preferences and we\'ll create a customized itinerary just for you',
  description: 'Our travel experts will design a personalized experience based on your interests, budget, and travel dates.',
  steps: [
    {
      id: 'destination',
      title: 'Choose Destination',
      description: 'Select your preferred destinations and travel style',
      icon: 'MapPin'
    },
    {
      id: 'details',
      title: 'Trip Details',
      description: 'Provide travel dates, group size, and preferences',
      icon: 'Calendar'
    },
    {
      id: 'contact',
      title: 'Contact Information',
      description: 'Share your details for personalized assistance',
      icon: 'User'
    },
    {
      id: 'confirmation',
      title: 'Confirmation',
      description: 'Review your request and submit for expert planning',
      icon: 'CheckCircle'
    }
  ],
  destinations: [
    { id: 'darjeeling', name: 'Darjeeling', description: 'Tea gardens, toy train, and mountain views', image: '/images/destinations/darjeeling.webp' },
    { id: 'sikkim', name: 'Sikkim', description: 'Monasteries, alpine lakes, and adventure sports', image: '/images/destinations/sikkim.webp' },
    { id: 'dooars', name: 'Dooars', description: 'Wildlife sanctuaries and forest lodges', image: '/images/destinations/dooars.webp' },
    { id: 'kalimpong', name: 'Kalimpong', description: 'Flower markets and panoramic views', image: '/images/destinations/kalimpong.webp' },
    { id: 'gangtok', name: 'Gangtok', description: 'Modern city with traditional charm', image: '/images/destinations/gangtok.webp' },
    { id: 'custom', name: 'Custom Package', description: 'Create your own unique itinerary', image: '/images/destinations/custom.webp' }
  ],
  travelStyles: [
    { id: 'family', name: 'Family Friendly', description: 'Safe and comfortable for all ages', icon: 'Users' },
    { id: 'adventure', name: 'Adventure', description: 'Trekking, rafting, and outdoor activities', icon: 'Mountain' },
    { id: 'cultural', name: 'Cultural', description: 'Monasteries, local traditions, and heritage', icon: 'Building' },
    { id: 'relaxation', name: 'Relaxation', description: 'Peaceful retreats and wellness', icon: 'Leaf' },
    { id: 'photography', name: 'Photography', description: 'Scenic spots and sunrise points', icon: 'Camera' },
    { id: 'wildlife', name: 'Wildlife', description: 'National parks and bird watching', icon: 'Binoculars' }
  ],
  accommodationTypes: [
    { id: 'hotel', name: 'Hotels', description: 'Comfortable rooms with modern amenities' },
    { id: 'resort', name: 'Resorts', description: 'Full-service properties with activities' },
    { id: 'homestay', name: 'Homestays', description: 'Local family experiences' },
    { id: 'heritage', name: 'Heritage Properties', description: 'Historic buildings with character' }
  ],
  budgetRanges: [
    { id: 'budget', name: 'Budget Friendly', range: '₹5,000 - ₹10,000 per person', description: 'Essential comfort and experiences' },
    { id: 'standard', name: 'Standard', range: '₹10,000 - ₹20,000 per person', description: 'Good balance of comfort and value' },
    { id: 'premium', name: 'Premium', range: '₹20,000 - ₹35,000 per person', description: 'Enhanced comfort and exclusive experiences' },
    { id: 'luxury', name: 'Luxury', range: '₹35,000+ per person', description: 'Ultimate comfort and personalized service' }
  ],
  formFields: {
    destination: {
      label: 'Select Destinations',
      placeholder: 'Choose one or more destinations',
      required: true,
      type: 'multiselect'
    },
    travelStyle: {
      label: 'Travel Style',
      placeholder: 'What type of experience do you prefer?',
      required: true,
      type: 'select'
    },
    startDate: {
      label: 'Travel Start Date',
      placeholder: 'Select your departure date',
      required: true,
      type: 'date'
    },
    duration: {
      label: 'Trip Duration',
      placeholder: 'How many days?',
      required: true,
      type: 'select',
      options: ['2-3 days', '4-5 days', '6-7 days', '8-10 days', '10+ days']
    },
    groupSize: {
      label: 'Group Size',
      placeholder: 'Number of travelers',
      required: true,
      type: 'number'
    },
    budget: {
      label: 'Budget Range',
      placeholder: 'Select your budget preference',
      required: true,
      type: 'select'
    },
    accommodation: {
      label: 'Accommodation Preference',
      placeholder: 'Choose your preferred stay type',
      required: false,
      type: 'select'
    },
    specialRequests: {
      label: 'Special Requirements',
      placeholder: 'Any specific needs or preferences? (dietary, accessibility, etc.)',
      required: false,
      type: 'textarea'
    },
    name: {
      label: 'Full Name',
      placeholder: 'Enter your full name',
      required: true,
      type: 'text'
    },
    email: {
      label: 'Email Address',
      placeholder: 'Enter your email address',
      required: true,
      type: 'email'
    },
    phone: {
      label: 'Phone Number',
      placeholder: 'Enter your phone number',
      required: true,
      type: 'tel'
    },
    city: {
      label: 'City',
      placeholder: 'Your current city',
      required: true,
      type: 'text'
    }
  },
  validationMessages: {
    required: 'This field is required',
    email: 'Please enter a valid email address',
    phone: 'Please enter a valid phone number',
    date: 'Please select a valid date',
    minDate: 'Travel date must be at least 7 days from today'
  },
  submitButton: {
    text: 'Get My Custom Itinerary',
    loadingText: 'Creating Your Perfect Trip...',
    successText: 'Request Submitted Successfully!'
  },
  successMessage: {
    headline: 'Thank You for Choosing Blissful Trails!',
    message: 'Our travel experts will review your requirements and contact you within 2 hours with a personalized itinerary.',
    nextSteps: [
      'Expert will call you within 2 hours',
      'Receive customized itinerary via email',
      'WhatsApp support for instant queries',
      'Flexible booking with easy modifications'
    ]
  },
  trustIndicators: [
    '2-Hour Response Guarantee',
    'No Hidden Charges',
    'Free Itinerary Planning',
    '24/7 Support During Travel'
  ]
} as const;
