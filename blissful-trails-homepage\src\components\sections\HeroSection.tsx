'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { HERO_CONTENT } from '@/data/content';
import { useScrollTo } from '@/lib/hooks';

export default function HeroSection() {
  const [isLoading, setIsLoading] = useState(false);
  const { scrollToElement } = useScrollTo();

  const handlePrimaryCTA = async () => {
    setIsLoading(true);
    // Scroll to booking form
    scrollToElement('booking-form');
    setIsLoading(false);
  };

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden pt-16 lg:pt-20">
      {/* Background Image with Optimization */}
      <div className="absolute inset-0 z-0">
        {/* Hero background - Clean temple architecture matching reference */}
        <div className="absolute inset-0">
          {/* Main background representing temple architecture from reference */}
          <div className="absolute inset-0 bg-gradient-to-br from-amber-50 via-orange-50 to-yellow-50" />

          {/* Temple architecture simulation - clean and elegant */}
          <div className="absolute inset-0 opacity-30">
            {/* Temple structure silhouettes */}
            <div className="absolute bottom-0 left-1/4 w-64 h-96 bg-gradient-to-t from-amber-800/50 to-amber-600/30 rounded-t-full transform -skew-x-2" />
            <div className="absolute bottom-0 right-1/4 w-48 h-80 bg-gradient-to-t from-orange-800/40 to-orange-600/20 rounded-t-full transform skew-x-1" />
            <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-72 h-[28rem] bg-gradient-to-t from-amber-900/60 to-amber-700/40 rounded-t-full" />
          </div>

          {/* Subtle architectural details */}
          <div className="absolute inset-0 opacity-15">
            <div className="absolute bottom-0 left-0 right-0 h-1/4 bg-gradient-to-t from-amber-900/30 to-transparent" />
            <div className="absolute top-1/3 left-1/2 transform -translate-x-1/2 w-1 h-1/3 bg-yellow-700/30" />
          </div>
        </div>
        {/* Clean overlay for text readability - matching reference */}
        <div className="absolute inset-0 bg-gradient-to-r from-dark-900/50 via-dark-900/30 to-dark-900/40" />
      </div>

      {/* Content - Updated to match reference design */}
      <div className="relative z-10 container-custom text-center text-white section-padding">
        <motion.h1
          className="text-hero-mobile md:text-hero font-heading font-bold mb-8 leading-tight max-w-5xl mx-auto"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          style={{ letterSpacing: '-0.02em' }}
        >
          Discover the Magic of Thailand: Your Ultimate Adventure Awaits
        </motion.h1>

        <motion.p
          className="text-lg md:text-xl mb-12 max-w-4xl mx-auto font-normal text-white/85 leading-relaxed"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          From ancient temples to pristine beaches, experience Thailand like never before with our expertly curated adventures and authentic local experiences.
        </motion.p>

        <motion.p
          className="text-lg md:text-xl mb-12 max-w-4xl mx-auto text-white/80 leading-relaxed"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          {HERO_CONTENT.description}
        </motion.p>

        {/* CTA Buttons - Updated to match reference design */}
        <motion.div
          className="flex flex-col sm:flex-row gap-4 justify-center mb-20"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          data-testid="cta-container"
        >
          <button
            type="button"
            onClick={handlePrimaryCTA}
            disabled={isLoading}
            className="bg-primary-500 hover:bg-primary-600 text-white font-semibold px-10 py-4 rounded-lg text-base min-w-[180px] transform hover:scale-105 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl"
          >
            {isLoading ? 'Loading...' : 'Explore'}
          </button>

          <button
            type="button"
            onClick={() => scrollToElement('packages')}
            className="bg-transparent hover:bg-white/10 text-white font-semibold px-10 py-4 rounded-lg border-2 border-white/40 hover:border-white/60 text-base min-w-[180px] transform hover:scale-105 transition-all duration-300"
          >
            Learn More
          </button>
        </motion.div>

        {/* Trust Badges */}
        <motion.div
          className="flex flex-wrap justify-center gap-6 mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
        >
          {HERO_CONTENT.trustBadges.map((badge, index) => (
            <div key={index} className="flex items-center gap-3 bg-white/15 backdrop-blur-md px-6 py-3 rounded-full border border-white/20 hover:bg-white/20 transition-all duration-300">
              <div className="w-2 h-2 bg-secondary-500 rounded-full animate-pulse" />
              <span className="text-sm font-medium text-white/90">{badge}</span>
            </div>
          ))}
        </motion.div>
      </div>

      {/* Scroll Indicator */}
      <motion.div
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2 cursor-pointer"
        animate={{ y: [0, 10, 0] }}
        transition={{ duration: 2, repeat: Infinity }}
        onClick={() => scrollToElement('packages')}
      >
        <div className="w-6 h-10 border-2 border-white/60 rounded-full flex justify-center hover:border-white transition-colors duration-300">
          <div className="w-1 h-3 bg-white/80 rounded-full mt-2" />
        </div>
      </motion.div>
    </section>
  );
}
